# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/util/tracking/camera_motion.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.util.tracking import motion_models_pb2 as mediapipe_dot_util_dot_tracking_dot_motion__models__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+mediapipe/util/tracking/camera_motion.proto\x12\tmediapipe\x1a+mediapipe/util/tracking/motion_models.proto\"\x86\x0b\n\x0c\x43\x61meraMotion\x12\x30\n\x0btranslation\x18\x01 \x01(\x0b\x32\x1b.mediapipe.TranslationModel\x12.\n\nsimilarity\x18\x02 \x01(\x0b\x32\x1a.mediapipe.SimilarityModel\x12;\n\x11linear_similarity\x18\x03 \x01(\x0b\x32 .mediapipe.LinearSimilarityModel\x12&\n\x06\x61\x66\x66ine\x18\x04 \x01(\x0b\x32\x16.mediapipe.AffineModel\x12)\n\nhomography\x18\x05 \x01(\x0b\x32\x15.mediapipe.Homography\x12\x38\n\x12mixture_homography\x18\x08 \x01(\x0b\x32\x1c.mediapipe.MixtureHomography\x12\x13\n\x0b\x66rame_width\x18\x1f \x01(\x02\x12\x14\n\x0c\x66rame_height\x18  \x01(\x02\x12\x41\n\x1bmixture_homography_spectrum\x18\x17 \x03(\x0b\x32\x1c.mediapipe.MixtureHomography\x12\x19\n\x11mixture_row_sigma\x18\n \x01(\x02\x12\x1c\n\x11\x61verage_magnitude\x18\x18 \x01(\x02:\x01\x30\x12\x1f\n\x14translation_variance\x18\x19 \x01(\x02:\x01\x30\x12\"\n\x17similarity_inlier_ratio\x18\x1d \x01(\x02:\x01\x30\x12)\n\x1esimilarity_strict_inlier_ratio\x18\x1e \x01(\x02:\x01\x30\x12 \n\x18\x61verage_homography_error\x18\x0b \x01(\x02\x12\"\n\x1ahomography_inlier_coverage\x18\x0c \x01(\x02\x12)\n!homography_strict_inlier_coverage\x18\x16 \x01(\x02\x12\x1f\n\x17mixture_inlier_coverage\x18\r \x03(\x02\x12\x1d\n\x15rolling_shutter_guess\x18\x0e \x01(\x02\x12(\n\x1crolling_shutter_motion_index\x18\x10 \x01(\x05:\x02-1\x12\x17\n\x0foverlay_indices\x18\x11 \x03(\x05\x12\x1a\n\x0eoverlay_domain\x18\x12 \x01(\x05:\x02\x31\x30\x12\x31\n\x04type\x18\x06 \x01(\x0e\x32\x1c.mediapipe.CameraMotion.Type:\x05VALID\x12<\n\x0foverridden_type\x18\x0f \x01(\x0e\x32\x1c.mediapipe.CameraMotion.Type:\x05VALID\x12\x10\n\x05\x66lags\x18\x13 \x01(\x05:\x01\x30\x12\x12\n\nblur_score\x18\x14 \x01(\x02\x12\x14\n\tbluriness\x18\x15 \x01(\x02:\x01\x30\x12#\n\x1b\x66rac_long_features_rejected\x18\x1a \x01(\x02\x12\x19\n\x0etimestamp_usec\x18\x1b \x01(\x03:\x01\x30\x12\x16\n\x0bmatch_frame\x18\x1c \x01(\x05:\x01\x30\"R\n\x04Type\x12\t\n\x05VALID\x10\x00\x12\x12\n\x0eUNSTABLE_HOMOG\x10\x01\x12\x10\n\x0cUNSTABLE_SIM\x10\x02\x12\x0c\n\x08UNSTABLE\x10\x03\x12\x0b\n\x07INVALID\x10\x04\"\xc3\x01\n\x05\x46lags\x12\x16\n\x12\x46LAG_SHOT_BOUNDARY\x10\x01\x12\x15\n\x11\x46LAG_BLURRY_FRAME\x10\x02\x12\x16\n\x12\x46LAG_MAJOR_OVERLAY\x10\x04\x12\x14\n\x10\x46LAG_SHARP_FRAME\x10\x08\x12\x1c\n\x18\x46LAG_SINGULAR_ESTIMATION\x10\x10\x12\x12\n\x0e\x46LAG_SHOT_FADE\x10 \x12\x13\n\x0f\x46LAG_DUPLICATED\x10@\x12\x16\n\x11\x46LAG_CENTER_FRAME\x10\x80\x01*\x04\x08\t\x10\n')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.util.tracking.camera_motion_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_CAMERAMOTION']._serialized_start=104
  _globals['_CAMERAMOTION']._serialized_end=1518
  _globals['_CAMERAMOTION_TYPE']._serialized_start=1232
  _globals['_CAMERAMOTION_TYPE']._serialized_end=1314
  _globals['_CAMERAMOTION_FLAGS']._serialized_start=1317
  _globals['_CAMERAMOTION_FLAGS']._serialized_end=1512
# @@protoc_insertion_point(module_scope)
